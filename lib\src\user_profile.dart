import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/chat.dart';
import 'package:tencent_cloud_chat_demo/src/pages/home_page.dart';
import 'package:tencent_cloud_chat_demo/src/pages/qrcode/qrcode.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/search.dart';
import 'package:tencent_cloud_chat_demo/src/tencent_page.dart';
import 'package:tencent_cloud_chat_demo/utils/commonUtils.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_demo/widgets/avatar.dart';
import 'package:tencent_cloud_chat_sdk/enum/offlinePushInfo.dart';
import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/profile_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/permission.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitProfile/profile_widget.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitProfile/widget/tim_uikit_profile_widget.dart';
import './widgets/input_dialog.dart';
import './widgets/confirm_dialog.dart';

class UserProfile extends StatefulWidget {
  final String userID;
  final ValueChanged<V2TimConversation>? onClickSendMessage;
  final ValueChanged<String>? onRemarkUpdate;
  final String? myId;

  const UserProfile({Key? key,
    required this.userID,
    this.onRemarkUpdate,
    this.myId,
    this.onClickSendMessage})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => UserProfileState();
}

class UserProfileState extends State<UserProfile> {
  final TIMUIKitProfileController _timuiKitProfileController =
  TIMUIKitProfileController();
  TUICallKit? _calling;
  final V2TIMManager sdkInstance = TIMUIKitCore.getSDKInstance();
  String? newUserMARK;
  bool isInBlackList = false;
  bool isPined = false;
  bool isDisturb = false;
  String? nameRemark;
  bool isSelf = false;

  Future<void> _checkBlackListStatus() async {
    try {
      final result = await sdkInstance.getFriendshipManager().getBlackList();
      debugPrint('获取黑名单列表: ${result.data}');
      if (result.code == 0 && result.data != null) {
        final isBlocked =
        result.data!.any((item) => item.userID == widget.userID);
        setState(() {
          isInBlackList = isBlocked;
        });
      }
    } catch (e) {
      debugPrint('获取黑名单状态失败: $e');
    }
  }

  // 获取置顶状态
  Future<void> _checkPinedStatus() async {
    try {
      final conversationManager = sdkInstance.getConversationManager();
      // 构建会话ID
      final conversationID = "c2c_${widget.userID}"; // C2C单聊的会话ID格式
      final result = await conversationManager.getConversation(
        conversationID: conversationID,
      );
      debugPrint('获取会话信息: ${result.data?.isPinned}');
      if (result.code == 0 && result.data != null) {
        setState(() {
          isPined = result.data!.isPinned ?? false;
        });
      }
    } catch (e) {
      debugPrint('获取置顶状态失败: $e');
    }
  }

// 获取免打扰状态
  Future<void> _checkDisturbStatus() async {
    try {
      final messageManager = sdkInstance.getMessageManager();
      final result = await messageManager.getC2CReceiveMessageOpt(
        userIDList: [widget.userID],
      );
      debugPrint('获取消息接收选项: ${result.data}');
      if (result.code == 0 && result.data != null && result.data!.isNotEmpty) {
        final opt = result.data![0];
        setState(() {
          // ReceiveMsgOptEnum:
          // V2TIM_RECEIVE_MESSAGE = 0, // 在线正常接收消息，离线时会有厂商的推送通知
          // V2TIM_NOT_RECEIVE_MESSAGE = 1, // 不会接收到消息
          // V2TIM_RECEIVE_NOT_NOTIFY_MESSAGE = 2, // 在线正常接收消息，离线不会有推送通知
          isDisturb = opt.c2CReceiveMessageOpt !=
              0; // 使用c2CReceiveMessageOpt而不是receiveOpt
        });
      }
    } catch (e) {
      debugPrint('获取免打扰状态失败: $e');
    }
  }

  _itemClick(String id, BuildContext context,
      V2TimConversation conversation) async {
    switch (id) {
      case "sendMsg":
        if (widget.onClickSendMessage != null) {
          widget.onClickSendMessage!(conversation);
        } else {
          print("left");
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    Chat(
                      selectedConversation: conversation,
                    ),
              ));
        }
        break;
      case "deleteFriend":
        _timuiKitProfileController.deleteFriend(widget.userID).then((res) {
          try {
            if (res == null) {
              throw Error();
            }
            if (res.resultCode == 0) {
              _timuiKitProfileController.loadData(widget.userID);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(TIM_t("好友删除成功"))),
              );
              Navigator.pop(context);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(TIM_t("好友删除失败"))),
              );
            }
          } catch (e) {
            debugPrint("执行过程中发生错误: $e");
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(TIM_t("好友删除失败"))),
            );
          }
        }).catchError((error) {
          ToastUtils.toast(TIM_t("好友添加失败"));
        });
        break;
      case "addUserToBlackList":
        bool oldIsAdd = isInBlackList;
        final isAdd = !isInBlackList; // 取反当前状态
        debugPrint(
            '准备${isAdd ? "添加到" : "从"}黑名单${isAdd
                ? "中"
                : "中移除"}: ${widget.userID}');
        try {
          setState(() {
            isInBlackList = isAdd;
          });

          _timuiKitProfileController
              .addUserToBlackList(isAdd, widget.userID);
        } catch (e) {
          debugPrint('黑名单操作异常: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(TIM_t("黑名单操作失败"))),
          );
          setState(() {
            isInBlackList = oldIsAdd;
          });
        }
        break;
      case "pinedConversation":
        bool oldIsAdd = isInBlackList;
        final isAdd = !isPined; // 取反当前状态
        final conversationID = "c2c_${widget.userID}";
        setState(() {
          isPined = isAdd;
        });
        debugPrint(
            '准备${isAdd ? "添加到" : "从"}置顶${isAdd
                ? "中"
                : "中移除"}: ${widget.userID}');
        try {
          _timuiKitProfileController
              .pinConversation(isAdd, conversationID)
              .then((result) {
            debugPrint('置顶操作结果: ${isAdd}');
            debugPrint('置顶操作结果: ${result.code}');
          });
        } catch (e) {
          debugPrint('置顶操作异常: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(TIM_t("置顶操作失败"))),
          );
          setState(() {
            isInBlackList = oldIsAdd;
          });
        }
        break;
      case "setMessageDisturb":
        bool oldIsAdd = isInBlackList;

        final isAdd = !isDisturb; // 取反当前状态
        debugPrint(
            '准备${isAdd ? "添加到" : "从"}免打扰${isAdd
                ? "中"
                : "中移除"}: ${widget.userID}');
        try {
          _timuiKitProfileController
              .setMessageDisturb(widget.userID, isAdd)
              .then((result) {
            debugPrint('免打扰操作结果: ${isAdd}');
            debugPrint('免打扰操作结果: ${result.code}');
            setState(() {
              isDisturb = isAdd;
            });
          });
        } catch (e) {
          debugPrint('免打扰操作异常: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(TIM_t("免打扰操作失败"))),
          );
          setState(() {
            isInBlackList = oldIsAdd;
          });
        }
        break;
      case "audioCall":
        print("widget.userID ${widget.userID}");

        OfflinePushInfo offlinePush = OfflinePushInfo(
          title: "",
          desc: TIM_t("邀请你语音通话"),
          ext: "{\"conversationID\": \"\"}",
          disablePush: false,
          androidFCMChannelID:'fcm_push_channel',
          ignoreIOSBadge: false,
        );
        await Permissions.checkPermission(context, Permission.microphone.value);
        TUIOfflinePushInfo tuiOfflinePushInfo =
        CommonUtils.convertTUIOfflinePushInfo(offlinePush);
        TUICallParams params = TUICallParams();
        params.offlinePushInfo = tuiOfflinePushInfo;
        await _calling?.call(widget.userID, TUICallMediaType.audio, params);
        break;
      case "videoCall":
        OfflinePushInfo offlinePush = OfflinePushInfo(
          title: "",
          desc: TIM_t("邀请你视频通话"),
          ext: "{\"conversationID\": \"\"}",
          disablePush: false,
          ignoreIOSBadge: false,
          androidFCMChannelID:'fcm_push_channel',
        );

        await Permissions.checkPermission(context, Permission.camera.value);
        await Permissions.checkPermission(context, Permission.microphone.value);
        TUIOfflinePushInfo tuiOfflinePushInfo =
        CommonUtils.convertTUIOfflinePushInfo(offlinePush);
        TUICallParams params = TUICallParams();
        params.offlinePushInfo = tuiOfflinePushInfo;
        _calling?.call(widget.userID, TUICallMediaType.video, params);
        break;
    }
  }

  _buildBottomOperationList(BuildContext context,
      V2TimConversation conversation, theme) {
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;

    List operationList = [
      {
        "label": TIM_t("发送消息"),
        "id": "sendMsg",
      },
      {
        "label": TIM_t("语音通话"),
        "id": "audioCall",
      },
      {
        "label": TIM_t("视频通话"),
        "id": "videoCall",
      },
    ];

    if (PlatformUtils().isWeb || PlatformUtils().isDesktop) {
      operationList = [
        {
          "label": TIM_t("发送消息"),
          "id": "sendMsg",
        }
      ];
    }

    return operationList.map((e) {
      return isWideScreen
          ? TIMUIKitProfileWidget.wideButton(
          smallCardMode: false,
          onPressed: () => _itemClick(e["id"] ?? "", context, conversation),
          text: e["label"] ?? "",
          color: e["id"] != "deleteFriend"
              ? theme.primaryColor
              : theme.cautionColor)
          : InkWell(
        onTap: () => _itemClick(e["id"] ?? "", context, conversation),
        child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 15),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                  bottom: BorderSide(color: theme.weakDividerColor))),
          child: Text(
            e["label"] ?? "",
            style: TextStyle(
                color: e["id"] != "deleteFriend"
                    ? theme.primaryColor
                    : theme.cautionColor,
                fontSize: 17),
          ),
        ),
      );
    }).toList();
  }

  _initTUICalling() async {
    _calling = TUICallKit.instance;
  }

  Future<void> _showRemarkDialog(V2TimFriendInfo friendInfo) async {
    print("--------------------------------");
    print(friendInfo.friendRemark);
    print("--------------------------------");
    final String? remarkName = await InputDialog.show(
      context: context,
      title: "修改备注",
      hintText: TIM_t("请输入备注名"),

      initialValue: friendInfo.friendRemark!,
    );
    if (remarkName != null && remarkName.isNotEmpty) {
      setState(() {
        nameRemark = remarkName;
      });
      final friendshipManager = sdkInstance.getFriendshipManager();
      final result = await friendshipManager.setFriendInfo(
          userID: widget.userID, friendRemark: remarkName);
      if (result.code == 0) {
        // 重新获取好友信息
        final getFriendInfoResult = await friendshipManager.getFriendsInfo(
            userIDList: [widget.userID]);
        if (getFriendInfoResult.code == 0 &&
            getFriendInfoResult.data != null &&
            getFriendInfoResult.data!.isNotEmpty) {
          print(getFriendInfoResult.data![0].friendInfo!.friendRemark);
          setState(() {
            friendInfo.friendRemark =
                getFriendInfoResult.data![0].friendInfo!.friendRemark;
            friendInfo.friendCustomInfo =
                getFriendInfoResult.data![0].friendInfo!.friendCustomInfo;
            friendInfo.userID = getFriendInfoResult.data![0].friendInfo!.userID;
            friendInfo.friendGroups =
                getFriendInfoResult.data![0].friendInfo!.friendGroups;
            friendInfo.userProfile =
                getFriendInfoResult.data![0].friendInfo!.userProfile;
          });
          _getFriendRemark(widget.userID);
        }
      }
    }
  }

  // 获取好友备注信息
  Future<void> _getFriendRemark(String userID) async {
    final friendshipManager = sdkInstance.getFriendshipManager();
    final result = await friendshipManager.getFriendsInfo(userIDList: [userID]);
    if (result.code == 0 && result.data != null && result.data!.isNotEmpty) {
      nameRemark = result.data![0].friendInfo?.friendRemark;
    }
  }

  // 删除好友
  Future<void> _deleteFriend() async {
    final bool? confirm = await ConfirmDialog.show(
      context: context,
      title: TIM_t("是否删除好友？"),
      content: TIM_t("删除好友后，好友信息将被删除，您将无法再收到好友的消息。"),
    );
    if (confirm == true) {
      _timuiKitProfileController.deleteFriend(widget.userID).then((res) {
        try {
          print("-----------------------");
          print(res?.resultCode);
          print("-----------------------");
          if (res == null) {
            throw Error();
          }
          if (res.resultCode == 0) {
            _timuiKitProfileController.loadData(widget.userID);
          }
          //跳转到home
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(

              builder: (context) => const HomePage(),
            ),
                (route) => false,
          );
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(TIM_t("好友删除失败"))),
          );
        }
      }).catchError((error) {
        ToastUtils.toast(TIM_t("好友添加失败"));
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _initTUICalling();
    _checkBlackListStatus();
    _checkPinedStatus();
    _checkDisturbStatus();
    _getFriendRemark(widget.userID);

    if (widget.myId == widget.userID) {
      isSelf = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Provider
        .of<DefaultThemeData>(context)
        .theme;
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    return TencentPage(
        child: Scaffold(
            appBar: null,
            body: Container(
              color: const Color(0xFFF9F9F9),
              child: Stack(
                children: [
                  const UserProfileLogo(),
                  SafeArea(
                      child:
                      Container(
                        height: double.infinity,
                        child: TIMUIKitProfile(
                          userID: widget.userID,
                          builder: (BuildContext context,
                              V2TimFriendInfo friendInfo,
                              V2TimConversation? conversation,
                              int friendType,
                              bool isMuted) {
                            final conversationData = conversation;
                            return SingleChildScrollView(
                              child: Column(
                                children: [
                                  userInfoCard(context, friendInfo),
                                  if(!isSelf)
                                    Container(
                                      child: Column(children: [
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 32),
                                          color: Colors.white,
                                          child: Column(
                                            children: [
                                              Container(
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                      color: Color(0xFFEEEEEE),
                                                      width: 1,
                                                    ),
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 16),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                    children: [
                                                      Text(TIM_t('加入黑名单'),
                                                          style: const TextStyle(
                                                            fontSize: 14,
                                                            color:
                                                            Color(0xFF666666),
                                                            fontWeight:
                                                            FontWeight.w500,
                                                          )),
                                                      SizedBox(
                                                        width: 52,
                                                        height: 26,
                                                        child: FittedBox(
                                                          fit: BoxFit.contain,
                                                          child: Switch(
                                                            value: isInBlackList,
                                                            onChanged:
                                                                (bool value) {
                                                              if (conversationData !=
                                                                  null) {
                                                                _itemClick(
                                                                    'addUserToBlackList',
                                                                    context,
                                                                    conversationData);
                                                              } else {
                                                                debugPrint(
                                                                    '会话数据为空');
                                                              }
                                                            },
                                                            activeColor:
                                                            Colors.white,
                                                            activeTrackColor:
                                                            theme.primaryColor,
                                                            inactiveThumbColor:
                                                            Colors.white,
                                                            inactiveTrackColor:
                                                            Colors.grey[300],
                                                            materialTapTargetSize:
                                                            MaterialTapTargetSize
                                                                .shrinkWrap,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                      color: Color(0xFFEEEEEE),
                                                      width: 1,
                                                    ),
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 16),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                    children: [
                                                      Text(TIM_t('置顶聊天'),
                                                          style: const TextStyle(
                                                            fontSize: 14,
                                                            color:
                                                            Color(0xFF666666),
                                                            fontWeight:
                                                            FontWeight.w500,
                                                          )),
                                                      SizedBox(
                                                        width: 52,
                                                        height: 26,
                                                        child: FittedBox(
                                                          fit: BoxFit.contain,
                                                          child: Switch(
                                                            value: isPined,
                                                            onChanged:
                                                                (bool value) {
                                                              if (conversationData !=
                                                                  null) {
                                                                _itemClick(
                                                                    'pinedConversation',
                                                                    context,
                                                                    conversationData);
                                                              } else {
                                                                debugPrint(
                                                                    '会话数据为空');
                                                              }
                                                            },
                                                            activeColor:
                                                            Colors.white,
                                                            activeTrackColor:
                                                            theme.primaryColor,
                                                            inactiveThumbColor:
                                                            Colors.white,
                                                            inactiveTrackColor:
                                                            Colors.grey[300],
                                                            materialTapTargetSize:
                                                            MaterialTapTargetSize
                                                                .shrinkWrap,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                decoration: const BoxDecoration(),
                                                child: Padding(
                                                  padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 16),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                    children: [
                                                      Text(TIM_t('消息免打扰'),
                                                          style: const TextStyle(
                                                            fontSize: 14,
                                                            color:
                                                            Color(0xFF666666),
                                                            fontWeight:
                                                            FontWeight.w500,
                                                          )),
                                                      SizedBox(
                                                        width: 52,
                                                        height: 26,
                                                        child: FittedBox(
                                                          fit: BoxFit.contain,
                                                          child: Switch(
                                                            value: isDisturb,
                                                            onChanged:
                                                                (bool value) {
                                                              if (conversationData !=
                                                                  null) {
                                                                _itemClick(
                                                                    'setMessageDisturb',
                                                                    context,
                                                                    conversationData);
                                                              } else {
                                                                debugPrint(
                                                                    '会话数据为空');
                                                              }
                                                            },
                                                            activeColor:
                                                            Colors.white,
                                                            activeTrackColor:
                                                            theme.primaryColor,
                                                            inactiveThumbColor:
                                                            Colors.white,
                                                            inactiveTrackColor:
                                                            Colors.grey[300],
                                                            materialTapTargetSize:
                                                            MaterialTapTargetSize
                                                                .shrinkWrap,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        // 发送消息按钮
                                        Container(
                                            padding: const EdgeInsets.only(
                                                left: 32, right: 32),
                                            color: Colors.white,
                                            child: Column(
                                              children: [
                                                Container(
                                                  decoration: const BoxDecoration(
                                                    border: Border(
                                                      bottom: BorderSide(
                                                        color: Color(
                                                            0xFFEEEEEE),
                                                        width: 1,
                                                      ),
                                                    ),
                                                  ),
                                                  child: InkWell(
                                                    onTap: () async {
                                                      if (conversationData !=
                                                          null) {
                                                        _itemClick(
                                                            'sendMsg',
                                                            context,
                                                            conversationData);
                                                      } else {
                                                        debugPrint(
                                                            '会话数据为空');
                                                      }
                                                    },
                                                    child: Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          vertical: 16),
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          TIM_t('发送消息'),
                                                          style: const TextStyle(
                                                            fontSize: 16,
                                                            color:
                                                            Color(0xff666666),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  decoration: const BoxDecoration(
                                                    border: Border(
                                                      bottom: BorderSide(
                                                        color: Color(
                                                            0xFFEEEEEE),
                                                        width: 1,
                                                      ),
                                                    ),
                                                  ),
                                                  child: InkWell(
                                                    onTap: () async {
                                                      if (conversationData !=
                                                          null) {
                                                        _itemClick(
                                                            'audioCall',
                                                            context,
                                                            conversationData);
                                                      } else {
                                                        debugPrint(
                                                            '会话数据为空');
                                                      }
                                                    },
                                                    child: Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          vertical: 16),
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          TIM_t('语音通话'),
                                                          style: const TextStyle(
                                                            fontSize: 16,
                                                            color:
                                                            Color(0xff666666),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  decoration: const BoxDecoration(
                                                    border: Border(
                                                      bottom: BorderSide(
                                                        color: Color(
                                                            0xFFEEEEEE),
                                                        width: 1,
                                                      ),
                                                    ),
                                                  ),
                                                  child: InkWell(
                                                    onTap: () async {
                                                      if (conversationData !=
                                                          null) {
                                                        _itemClick(
                                                            'videoCall',
                                                            context,
                                                            conversationData);
                                                      } else {
                                                        debugPrint(
                                                            '会话数据为空');
                                                      }
                                                    },
                                                    child: Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          vertical: 16),
                                                      decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          TIM_t('视频通话'),
                                                          style: const TextStyle(
                                                            fontSize: 16,
                                                            color:
                                                            Color(0xff666666),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  color: Colors.white,
                                                  child: InkWell(
                                                    onTap: () async {
                                                      _deleteFriend();
                                                    },
                                                    child: Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          vertical: 16),
                                                      decoration:
                                                      const BoxDecoration(
                                                        color: Colors.white,
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          TIM_t('删除好友'),
                                                          style: const TextStyle(
                                                            fontSize: 16,
                                                            color:
                                                            Color(0xffFF7677),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            )),
                                      ]),
                                    ),
                                ],
                              ),
                            );
                          },
                          profileWidgetBuilder: ProfileWidgetBuilder(
                              searchBar: (conversation) =>
                                  TIMUIKitProfileWidget.searchBar(
                                      context, conversation, false,
                                      handleTap: () {
                                        return;
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  Search(
                                                      conversation: conversation,
                                                      onTapConversation:
                                                          (
                                                          V2TimConversation conversation,
                                                          [V2TimMessage? targetMsg]) {
                                                        Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                              builder: (
                                                                  context) =>
                                                                  Chat(
                                                                    selectedConversation:
                                                                    conversation,
                                                                    initFindingMsg: targetMsg,
                                                                  ),
                                                            ));
                                                      }),
                                            ));
                                      }),
                              customBuilderOne: (bool isFriend,
                                  V2TimFriendInfo friendInfo,
                                  V2TimConversation conversation) {
                                // If you don't allow sending message when friendship not exist,
                                // please not comment the following lines.

                                // if(!isFriend){
                                //   return Container();
                                // }
                                return Container(
                                  margin: isWideScreen
                                      ? const EdgeInsets.only(top: 30)
                                      : null,
                                  child: Column(
                                      children: _buildBottomOperationList(
                                          context, conversation, theme)),
                                );
                              }),
                          controller: _timuiKitProfileController,
                        ),
                      )),

                ],
              ),
            )),
        name: "friendProfile");
  }

  Widget userInfoCard(BuildContext context, V2TimFriendInfo friendInfo) {
    final V2TimUserFullInfo? userInfo = friendInfo.userProfile;
    return Column(children: [
      Padding(
          padding: const EdgeInsets.only(left: 16),
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset('assets/icon_left_back.png', width: 24, height: 24),
              ],
            ),
          )),
      const SizedBox(height: 23),
      Padding(
        padding: const EdgeInsets.only(left: 16),
        child: Row(
          children: [
            Avatar(
              avatarUrl: userInfo?.faceUrl,
              size: 52,
              radius: 26,
              showBorder: true,
              borderColor: Colors.white,
              borderWidth: 2,
            ),
            const SizedBox(width: 16),
            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(
                userInfo?.nickName ?? '',
                style: const TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'ID: ${userInfo?.userID ?? ''}',
                style: const TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 4),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: TIM_t('个性签名'),
                      style: const TextStyle(
                        color: Color(0xFFFFFFFF),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    TextSpan(
                      text: ':' + (userInfo?.selfSignature ?? ''),
                      style: const TextStyle(
                        color: Color(0xFFFFFFFF),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ])
          ],
        ),
      ),
      const SizedBox(height: 24),
      Container(
          padding: const EdgeInsets.only(top: 10, left: 32, right: 32),
          decoration: const BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Column(children: [
            GestureDetector(
              onTap: () => _showRemarkDialog(friendInfo),
              child: Container(
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1,
                    ),
                  ),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(TIM_t('备注')),
                    Row(
                      children: [
                        if (nameRemark != null && nameRemark!.isNotEmpty)
                          Text(nameRemark ?? ''),
                        Image.asset('assets/remark_name.png',
                            width: 16, height: 16)
                      ],
                    )
                  ],
                ),
              ),
            ),
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFEEEEEE),
                    width: 1,
                  ),
                ),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(TIM_t('性别')),
                  Text(
                    userInfo?.gender == null || userInfo?.gender == 0
                        ? TIM_t('未填写')
                        : userInfo?.gender == 1
                        ? TIM_t('男')
                        : TIM_t('女'),
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF666666),
                      fontWeight: FontWeight.w500,
                    ),
                  )
                ],
              ),
            ),
            GestureDetector(
              onTap:(){
                if(userInfo == null){
                  return;
                }
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => Qrcode(userInfo: userInfo),
                  ),
                );
              },
              child: Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFEEEEEE),
                    width: 1,
                  ),
                ),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(TIM_t('二维码')),
                ],
              ),
            )),

            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(TIM_t('生日'),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF666666),
                        fontWeight: FontWeight.w500,
                      )),
                ],
              ),
            ),

          ])),
      const SizedBox(height: 16),
    ]);
  }
}

class UserProfileLogo extends StatelessWidget {
  const UserProfileLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          child: Image.asset(
            'assets/user_profile_bg.png',
            width: MediaQuery
                .of(context)
                .size
                .width,
            height: 204,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }
}
